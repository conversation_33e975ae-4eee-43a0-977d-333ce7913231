"""
Base agent implementation for GAAPF.

This module defines the base agent class and factory function
for creating specialized agents that integrate with vinagent.
"""

from typing import Dict, List, Optional, Any, Type, Union
import asyncio
import json
import os
from pathlib import Path
from abc import ABC, abstractmethod

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage

# Import vinagent components
from vinagent.agent import Agent as VinAgent
from vinagent.memory import Memory


class BaseGAAPFAgent(ABC):
    """
    Base agent class for GAAPF that extends vinagent capabilities.

    All specialized GAAPF agents inherit from this class and integrate
    with vinagent's Agent system while adding GAAPF-specific functionality.
    """

    def __init__(
        self,
        agent_type: str,
        llm: BaseLanguageModel,
        user_profile: Dict[str, Any],
        framework: str,
        module_id: str,
        session_id: str,
        is_primary: bool = False,
        tools: List[str] = None,
        memory_path: Optional[Path] = None
    ):
        """
        Initialize the GAAPF agent.

        Args:
            agent_type: Type of agent (e.g., "instructor", "code_assistant")
            llm: Language model to use
            user_profile: User profile information
            framework: Target framework (e.g., "langchain", "langgraph")
            module_id: Current learning module
            session_id: Session identifier
            is_primary: Whether this is a primary agent in the constellation
            tools: List of tools this agent can use
            memory_path: Path to agent's memory file
        """
        self.agent_type = agent_type
        self.user_profile = user_profile
        self.framework = framework
        self.module_id = module_id
        self.session_id = session_id
        self.is_primary = is_primary

        # Create the underlying vinagent Agent
        self.vinagent = VinAgent(
            llm=llm,
            tools=tools or [],
            description=self._get_agent_description(),
            skills=self._get_agent_skills(),
            memory_path=str(memory_path) if memory_path else None,
            is_reset_memory=False
        )

        # Set user ID for memory management
        if user_profile.get("user_id"):
            self.vinagent.user_id = user_profile["user_id"]

    @abstractmethod
    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        pass

    @abstractmethod
    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        pass

    @abstractmethod
    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.

        Args:
            content: Agent's response content
            user_message: Original user message

        Returns:
            Dictionary with handoff analysis results
        """
        pass

    async def process_message(
        self,
        message: str,
        context: Dict[str, Any] = None,
        save_memory: bool = True
    ) -> Dict[str, Any]:
        """
        Process a user message using the underlying vinagent.

        Args:
            message: User message
            context: Additional context information
            save_memory: Whether to save the interaction to memory

        Returns:
            Dictionary containing response and metadata
        """
        # Enhance the message with context if provided
        enhanced_message = self._enhance_message_with_context(message, context)

        # Process through vinagent
        response = await self.vinagent.ainvoke(
            query=enhanced_message,
            is_save_memory=save_memory,
            user_id=self.user_profile.get("user_id", "unknown_user")
        )

        # Analyze for potential handoffs
        handoff_analysis = self._analyze_content_for_handoff(
            content=response.content if hasattr(response, 'content') else str(response),
            user_message=message
        )

        return {
            "response": response,
            "agent_type": self.agent_type,
            "handoff_analysis": handoff_analysis,
            "context": context,
            "session_id": self.session_id
        }

    def _enhance_message_with_context(self, message: str, context: Dict[str, Any] = None) -> str:
        """
        Enhance the user message with relevant context.

        Args:
            message: Original user message
            context: Additional context information

        Returns:
            Enhanced message with context
        """
        if not context:
            return message

        context_parts = []

        # Add framework context
        if self.framework:
            context_parts.append(f"Framework: {self.framework}")

        # Add module context
        if self.module_id:
            context_parts.append(f"Learning Module: {self.module_id}")

        # Add user skill level context
        if self.user_profile.get("skill_level"):
            context_parts.append(f"User Skill Level: {self.user_profile['skill_level']}")

        # Add learning style context
        if self.user_profile.get("learning_style"):
            context_parts.append(f"Learning Style: {self.user_profile['learning_style']}")

        # Add any additional context
        for key, value in context.items():
            if key not in ["framework", "module_id", "skill_level", "learning_style"]:
                context_parts.append(f"{key}: {value}")

        if context_parts:
            context_str = "\n".join([f"- {part}" for part in context_parts])
            return f"Context:\n{context_str}\n\nUser Question: {message}"

        return message

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.

        Args:
            message: User message

        Returns:
            Confidence score between 0.0 and 1.0
        """
        # Base implementation - subclasses should override
        return 0.5


# Agent factory function
async def create_agent(
    agent_type: str,
    user_profile: Dict[str, Any],
    framework: str,
    module_id: str,
    session_id: str,
    llm: BaseLanguageModel,
    is_primary: bool = False
) -> BaseGAAPFAgent:
    """
    Create a specialized agent based on type.

    Args:
        agent_type: Type of agent to create
        user_profile: User profile information
        framework: Target framework
        module_id: Current learning module
        session_id: Session identifier
        llm: Language model to use
        is_primary: Whether this is a primary agent

    Returns:
        Specialized agent instance
    """
    # Import specialized agents
    from gaapf.agents.instructor_agent import InstructorAgent
    from gaapf.agents.code_assistant_agent import CodeAssistantAgent
    from gaapf.agents.documentation_expert_agent import DocumentationExpertAgent
    from gaapf.agents.practice_facilitator_agent import PracticeFacilitatorAgent
    from gaapf.agents.mentor_agent import MentorAgent

    # Agent type mapping
    agent_classes = {
        "instructor": InstructorAgent,
        "code_assistant": CodeAssistantAgent,
        "documentation_expert": DocumentationExpertAgent,
        "practice_facilitator": PracticeFacilitatorAgent,
        "mentor": MentorAgent,
        # Additional agents will be implemented
        "assessment": BaseGAAPFAgent,  # Placeholder
        "research_assistant": BaseGAAPFAgent,  # Placeholder
        "project_guide": BaseGAAPFAgent,  # Placeholder
        "troubleshooter": BaseGAAPFAgent,  # Placeholder
        "motivational_coach": BaseGAAPFAgent,  # Placeholder
        "knowledge_synthesizer": BaseGAAPFAgent,  # Placeholder
        "progress_tracker": BaseGAAPFAgent,  # Placeholder
    }

    agent_class = agent_classes.get(agent_type)
    if not agent_class:
        raise ValueError(f"Unknown agent type: {agent_type}")

    # Create memory path for the agent
    memory_path = Path(f"data/memory/{session_id}_{agent_type}_memory.json")
    memory_path.parent.mkdir(parents=True, exist_ok=True)

    # Get tools for the agent type
    tools = _get_agent_tools(agent_type, framework)

    # Create and return the agent
    if agent_class == BaseGAAPFAgent:
        # For placeholder agents, create a simple implementation
        class PlaceholderAgent(BaseGAAPFAgent):
            def _get_agent_description(self) -> str:
                return f"I am a {agent_type.replace('_', ' ')} agent helping with {framework} learning."

            def _get_agent_skills(self) -> List[str]:
                return [f"Specialized in {agent_type.replace('_', ' ')} tasks"]

            def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
                return {"needs_handoff": False, "suggested_agent": None, "confidence": 0.5}

        return PlaceholderAgent(
            agent_type=agent_type,
            llm=llm,
            user_profile=user_profile,
            framework=framework,
            module_id=module_id,
            session_id=session_id,
            is_primary=is_primary,
            tools=tools,
            memory_path=memory_path
        )
    else:
        return agent_class(
            agent_type=agent_type,
            llm=llm,
            user_profile=user_profile,
            framework=framework,
            module_id=module_id,
            session_id=session_id,
            is_primary=is_primary,
            tools=tools,
            memory_path=memory_path
        )


def _get_agent_tools(agent_type: str, framework: str) -> List[str]:
    """
    Get the list of tools for a specific agent type and framework.

    Args:
        agent_type: Type of agent
        framework: Target framework

    Returns:
        List of tool names
    """
    # For now, return empty list to avoid missing tool errors
    # Tools can be added later when they are properly implemented
    return []