"""
Instructor agent implementation for GAAPF.

This agent specializes in providing theoretical knowledge and explanations
for Python AI frameworks, with a focus on conceptual understanding.
"""

from typing import Dict, List, Optional, Any
import re

from gaapf.agents.base_agent import BaseGAAPFAgent


class InstructorAgent(BaseGAAPFAgent):
    """
    Instructor agent that provides theoretical knowledge and explanations.

    This agent specializes in:
    - Explaining concepts and theoretical foundations
    - Providing structured learning content
    - Breaking down complex topics into digestible parts
    - Offering conceptual frameworks and mental models
    """

    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return (
            f"You are an expert {self.framework} instructor and educator. Your role is to provide "
            "clear, comprehensive theoretical explanations and conceptual understanding. You excel at "
            "breaking down complex topics into digestible parts, providing structured learning content, "
            "and helping users build strong conceptual foundations. You adapt your explanations to the "
            f"user's skill level ({self.user_profile.get('skill_level', 'intermediate')}) and learning "
            f"style ({self.user_profile.get('learning_style', 'mixed')}). Focus on the 'why' and 'how' "
            "behind concepts, not just the 'what'."
        )

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Expert knowledge of {self.framework} concepts and architecture",
            "Breaking down complex topics into understandable components",
            "Providing structured learning progressions",
            "Explaining theoretical foundations and design patterns",
            "Adapting explanations to different skill levels",
            "Creating conceptual frameworks and mental models",
            "Connecting new concepts to existing knowledge"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.

        Args:
            content: Agent's response content
            user_message: Original user message

        Returns:
            Dictionary with handoff analysis results
        """
        # Keywords that suggest need for other agents
        code_keywords = ["code", "example", "implement", "write", "build", "create", "function", "class"]
        practice_keywords = ["practice", "exercise", "try", "hands-on", "tutorial", "step-by-step"]
        docs_keywords = ["documentation", "docs", "reference", "api", "official"]
        troubleshoot_keywords = ["error", "bug", "problem", "issue", "fix", "debug", "not working"]

        user_lower = user_message.lower()
        content_lower = content.lower()

        # Check for code-related requests
        if any(keyword in user_lower for keyword in code_keywords):
            if "example" in user_lower or "show me" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "code_assistant",
                    "confidence": 0.8,
                    "reason": "User is asking for code examples or implementation"
                }

        # Check for practice-related requests
        if any(keyword in user_lower for keyword in practice_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "practice_facilitator",
                "confidence": 0.7,
                "reason": "User wants hands-on practice or exercises"
            }

        # Check for documentation requests
        if any(keyword in user_lower for keyword in docs_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "documentation_expert",
                "confidence": 0.6,
                "reason": "User is looking for official documentation or references"
            }

        # Check for troubleshooting needs
        if any(keyword in user_lower for keyword in troubleshoot_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "troubleshooter",
                "confidence": 0.9,
                "reason": "User has a problem that needs troubleshooting"
            }

        # Check if response suggests practical application
        if any(phrase in content_lower for phrase in ["try this", "implement", "here's how to"]):
            return {
                "needs_handoff": True,
                "suggested_agent": "code_assistant",
                "confidence": 0.6,
                "reason": "Response suggests practical implementation"
            }

        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Content is appropriate for theoretical instruction"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.

        Args:
            message: User message

        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()

        # High confidence keywords
        high_confidence_keywords = [
            "what is", "explain", "how does", "why", "concept", "theory",
            "understand", "learn about", "tell me about", "difference between",
            "architecture", "design pattern", "principle", "foundation"
        ]

        # Medium confidence keywords
        medium_confidence_keywords = [
            "overview", "introduction", "basics", "fundamentals", "getting started",
            "comparison", "pros and cons", "advantages", "disadvantages"
        ]

        # Low confidence keywords (better handled by other agents)
        low_confidence_keywords = [
            "code", "implement", "build", "create", "example", "tutorial",
            "practice", "exercise", "debug", "error", "fix", "problem"
        ]

        # Calculate confidence based on keyword presence
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        elif any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        elif any(keyword in message_lower for keyword in low_confidence_keywords):
            return 0.3
        else:
            # Default confidence for general questions
            return 0.6