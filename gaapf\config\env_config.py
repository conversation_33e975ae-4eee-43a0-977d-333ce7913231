"""
Environment configuration for GAAPF.

This module handles loading environment variables from .env files
and provides configuration management for the GAAPF system.
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv

from langchain_core.language_models.base import BaseLanguageModel


class EnvironmentConfig:
    """
    Environment configuration manager for GAAPF.
    
    Handles loading environment variables from .env files and provides
    convenient access to configuration values.
    """
    
    def __init__(self, env_file: Optional[Path] = None):
        """
        Initialize the environment configuration.
        
        Args:
            env_file: Path to .env file (if None, looks for .env in current directory)
        """
        self.env_file = env_file or Path(".env")
        self.load_environment()
    
    def load_environment(self):
        """Load environment variables from .env file."""
        if self.env_file.exists():
            load_dotenv(self.env_file)
            print(f"✅ Loaded environment variables from {self.env_file}")
        else:
            print(f"⚠️  No .env file found at {self.env_file}")
            print(f"   Copy .env.example to .env and configure your API keys")
    
    @property
    def openai_api_key(self) -> Optional[str]:
        """Get OpenAI API key."""
        return os.getenv("OPENAI_API_KEY")
    
    @property
    def google_api_key(self) -> Optional[str]:
        """Get Google Gemini API key."""
        return os.getenv("GOOGLE_API_KEY")
    
    @property
    def anthropic_api_key(self) -> Optional[str]:
        """Get Anthropic Claude API key."""
        return os.getenv("ANTHROPIC_API_KEY")

    @property
    def together_api_key(self) -> Optional[str]:
        """Get Together AI API key."""
        return os.getenv("TOGETHER_API_KEY")

    @property
    def tavily_api_key(self) -> Optional[str]:
        """Get Tavily Search API key."""
        return os.getenv("TAVILY_API_KEY")
    
    @property
    def default_llm_provider(self) -> str:
        """Get default LLM provider."""
        return os.getenv("DEFAULT_LLM_PROVIDER", "google")
    
    @property
    def openai_model(self) -> str:
        """Get OpenAI model name."""
        return os.getenv("OPENAI_MODEL", "gpt-4-turbo")
    
    @property
    def google_model(self) -> str:
        """Get Google model name."""
        return os.getenv("GOOGLE_MODEL", "gemini-1.5-pro")
    
    @property
    def anthropic_model(self) -> str:
        """Get Anthropic model name."""
        return os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229")

    @property
    def together_model(self) -> str:
        """Get Together AI model name."""
        return os.getenv("TOGETHER_MODEL", "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free")

    @property
    def data_path(self) -> Path:
        """Get data directory path."""
        return Path(os.getenv("GAAPF_DATA_PATH", "data"))
    
    @property
    def log_level(self) -> str:
        """Get log level."""
        return os.getenv("LOG_LEVEL", "INFO")
    
    @property
    def max_tokens(self) -> int:
        """Get maximum tokens for LLM responses."""
        return int(os.getenv("MAX_TOKENS", "4000"))
    
    @property
    def llm_temperature(self) -> float:
        """Get temperature for LLM responses."""
        return float(os.getenv("LLM_TEMPERATURE", "0.2"))
    
    @property
    def llm_timeout(self) -> int:
        """Get timeout for LLM requests."""
        return int(os.getenv("LLM_TIMEOUT", "60"))
    
    @property
    def debug_mode(self) -> bool:
        """Get debug mode setting."""
        return os.getenv("DEBUG_MODE", "false").lower() == "true"
    
    def get_available_llm_providers(self) -> Dict[str, bool]:
        """
        Get available LLM providers based on configured API keys.

        Returns:
            Dictionary mapping provider names to availability
        """
        return {
            "openai": bool(self.openai_api_key),
            "google": bool(self.google_api_key),
            "anthropic": bool(self.anthropic_api_key),
            "together": bool(self.together_api_key)
        }
    
    def create_llm(self, provider: Optional[str] = None) -> BaseLanguageModel:
        """
        Create an LLM instance based on the provider.
        
        Args:
            provider: LLM provider to use (if None, uses default)
            
        Returns:
            Configured LLM instance
            
        Raises:
            ValueError: If provider is not available or API key is missing
        """
        provider = provider or self.default_llm_provider
        available_providers = self.get_available_llm_providers()
        
        if provider not in available_providers:
            raise ValueError(f"Unknown LLM provider: {provider}")
        
        if not available_providers[provider]:
            raise ValueError(f"API key not configured for provider: {provider}")
        
        if provider == "openai":
            from langchain_openai import ChatOpenAI
            return ChatOpenAI(
                model=self.openai_model,
                temperature=self.llm_temperature,
                max_tokens=self.max_tokens,
                timeout=self.llm_timeout,
                api_key=self.openai_api_key
            )
        
        elif provider == "google":
            from langchain_google_genai import ChatGoogleGenerativeAI
            return ChatGoogleGenerativeAI(
                model=self.google_model,
                temperature=self.llm_temperature,
                max_tokens=self.max_tokens,
                timeout=self.llm_timeout,
                google_api_key=self.google_api_key
            )
        
        elif provider == "anthropic":
            from langchain_anthropic import ChatAnthropic
            return ChatAnthropic(
                model=self.anthropic_model,
                temperature=self.llm_temperature,
                max_tokens=self.max_tokens,
                timeout=self.llm_timeout,
                anthropic_api_key=self.anthropic_api_key
            )

        elif provider == "together":
            from langchain_together import ChatTogether
            return ChatTogether(
                model=self.together_model,
                temperature=self.llm_temperature,
                max_tokens=self.max_tokens,
                timeout=self.llm_timeout,
                together_api_key=self.together_api_key
            )

        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate the current configuration.
        
        Returns:
            Dictionary with validation results
        """
        available_providers = self.get_available_llm_providers()
        has_any_llm = any(available_providers.values())
        
        validation = {
            "valid": has_any_llm,
            "available_providers": available_providers,
            "default_provider_available": available_providers.get(self.default_llm_provider, False),
            "tavily_available": bool(self.tavily_api_key),
            "warnings": [],
            "errors": []
        }
        
        if not has_any_llm:
            validation["errors"].append("No LLM API keys configured. At least one is required.")
        
        if not validation["default_provider_available"]:
            validation["warnings"].append(f"Default LLM provider '{self.default_llm_provider}' is not available.")
        
        if not validation["tavily_available"]:
            validation["warnings"].append("Tavily API key not configured. Search functionality will be limited.")
        
        return validation
    
    def print_configuration_status(self):
        """Print the current configuration status."""
        validation = self.validate_configuration()
        
        print("\n🔧 GAAPF Configuration Status")
        print("=" * 50)
        
        # LLM Providers
        print("🤖 LLM Providers:")
        for provider, available in validation["available_providers"].items():
            status = "✅" if available else "❌"
            print(f"  {status} {provider.title()}")
        
        # Default provider
        default_status = "✅" if validation["default_provider_available"] else "⚠️"
        print(f"\n🎯 Default Provider: {default_status} {self.default_llm_provider}")
        
        # Tools
        tavily_status = "✅" if validation["tavily_available"] else "⚠️"
        print(f"🔍 Tavily Search: {tavily_status}")
        
        # Warnings and errors
        if validation["warnings"]:
            print(f"\n⚠️  Warnings:")
            for warning in validation["warnings"]:
                print(f"  - {warning}")
        
        if validation["errors"]:
            print(f"\n❌ Errors:")
            for error in validation["errors"]:
                print(f"  - {error}")
        
        if validation["valid"]:
            print(f"\n🎉 Configuration is valid! Ready to use GAAPF.")
        else:
            print(f"\n💥 Configuration has errors. Please fix them before using GAAPF.")


# Global configuration instance
config = EnvironmentConfig()


def get_config() -> EnvironmentConfig:
    """Get the global configuration instance."""
    return config


def reload_config(env_file: Optional[Path] = None) -> EnvironmentConfig:
    """Reload the global configuration."""
    global config
    config = EnvironmentConfig(env_file)
    return config
