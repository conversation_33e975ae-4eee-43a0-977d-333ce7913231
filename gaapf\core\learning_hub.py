"""
Learning Hub Core for GAAPF.

This module implements the Learning Hub Core architecture,
which serves as the central coordination point for the GAAPF system.
It manages interactions between users, agents, and other system components.
"""

from typing import Dict, List, Optional, Any
import asyncio
import json
import os
import uuid
from pathlib import Path

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage

from gaapf.core.constellation import ConstellationManager, ConstellationType
from gaapf.core.temporal_state import TemporalStateManager, EffectivenessMetrics
from gaapf.config.user_profiles import UserProfile
from gaapf.config.env_config import get_config


class LearningHubCore:
    """
    Central coordination hub for the GAAPF system.
    
    The LearningHubCore is responsible for:
    1. Managing user sessions and profiles
    2. Coordinating between constellation manager and temporal state manager
    3. Processing user messages and routing them to appropriate agents
    4. Tracking learning progress and metrics
    """
    
    def __init__(
        self,
        llm: Optional[BaseLanguageModel] = None,
        data_path: Path = Path("data"),
        config_path: Optional[Path] = None
    ):
        """
        Initialize the LearningHubCore.
        
        Args:
            llm: Language model to use (if None, will be initialized later)
            data_path: Path to store data
            config_path: Path to configuration files
        """
        self.llm = llm
        self.data_path = data_path
        self.config_path = config_path or data_path / "config"
        
        self.constellation_manager = None
        self.temporal_state_manager = None
        
        self.active_sessions = {}
        self.user_profiles = {}
        
    async def initialize(self):
        """Initialize the learning hub and its components."""
        # Create necessary directories
        os.makedirs(self.data_path, exist_ok=True)
        os.makedirs(self.config_path, exist_ok=True)
        os.makedirs(self.data_path / "user_profiles", exist_ok=True)
        os.makedirs(self.data_path / "sessions", exist_ok=True)
        
        # Initialize managers
        self.constellation_manager = ConstellationManager(
            config_path=self.config_path / "constellation_configs.json"
        )
        
        self.temporal_state_manager = TemporalStateManager(
            data_path=self.data_path / "temporal_patterns"
        )
        
        # Load user profiles
        await self._load_user_profiles()
        
    async def _load_user_profiles(self):
        """Load user profiles from storage."""
        profiles_path = self.data_path / "user_profiles"
        
        for file in profiles_path.glob("*.json"):
            try:
                with open(file, "r") as f:
                    profile_data = json.load(f)
                    user_id = file.stem
                    self.user_profiles[user_id] = UserProfile.from_dict(profile_data)
            except Exception as e:
                print(f"Error loading user profile {file}: {e}")
                
    async def save_user_profile(self, profile: UserProfile):
        """
        Save a user profile to storage.
        
        Args:
            profile: User profile to save
        """
        profiles_path = self.data_path / "user_profiles"
        os.makedirs(profiles_path, exist_ok=True)
        
        try:
            with open(profiles_path / f"{profile.user_id}.json", "w") as f:
                json.dump(profile.to_dict(), f, indent=2)
                
            # Update in-memory cache
            self.user_profiles[profile.user_id] = profile
        except Exception as e:
            print(f"Error saving user profile {profile.user_id}: {e}")
            
    async def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """
        Get a user profile by ID.
        
        Args:
            user_id: User identifier
            
        Returns:
            User profile if found, None otherwise
        """
        return self.user_profiles.get(user_id)
        
    async def create_session(
        self,
        user_id: str,
        framework: str,
        module_id: str,
        llm: Optional[BaseLanguageModel] = None
    ) -> str:
        """
        Create a new learning session.
        
        Args:
            user_id: User identifier
            framework: Target framework
            module_id: Learning module
            llm: Language model to use (if None, uses the default)
            
        Returns:
            Session identifier
        """
        # Get user profile
        profile = await self.get_user_profile(user_id)
        if not profile:
            raise ValueError(f"User profile not found for {user_id}")
            
        # Generate session ID
        session_id = str(uuid.uuid4())
        
        # Determine optimal constellation type
        constellation_type, confidence = await self.temporal_state_manager.optimize_constellation_selection(
            user_profile=profile.to_dict(),
            framework=framework,
            module_id=module_id,
            session_context={}
        )
        
        # Use provided LLM or default
        session_llm = llm or self.llm
        if not session_llm:
            raise ValueError("No language model provided")
            
        # Create constellation
        agents = await self.constellation_manager.create_constellation(
            constellation_type=constellation_type,
            user_profile=profile.to_dict(),
            framework=framework,
            module_id=module_id,
            session_id=session_id,
            llm=session_llm
        )
        
        # Store session information
        self.active_sessions[session_id] = {
            "user_id": user_id,
            "framework": framework,
            "module_id": module_id,
            "constellation_type": constellation_type,
            "start_time": asyncio.get_event_loop().time(),
            "messages": [],
            "metrics": EffectivenessMetrics()
        }
        
        return session_id
        
    async def process_message(
        self,
        user_id: str,
        message: str,
        session_id: Optional[str] = None,
        framework: Optional[str] = None,
        module_id: Optional[str] = None
    ) -> Dict:
        """
        Process a user message.
        
        Args:
            user_id: User identifier
            message: User message
            session_id: Session identifier (if None, creates a new session)
            framework: Target framework (required if session_id is None)
            module_id: Learning module (required if session_id is None)
            
        Returns:
            Processing result
        """
        # Check if message is None or empty
        if not message:
            return {
                "session_id": session_id or str(uuid.uuid4()),
                "response": "I didn't receive a message. Could you please try again?",
                "agent_path": ["system"]
            }
            
        # Check if session exists
        if session_id and session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
            
        # Create a new session if needed
        if not session_id:
            if not framework or not module_id:
                raise ValueError("Framework and module_id are required when creating a new session")
                
            session_id = await self.create_session(
                user_id=user_id,
                framework=framework,
                module_id=module_id,
                llm=self.llm
            )
            
        # Get session information
        session = self.active_sessions[session_id]
        
        # Store message in session history
        session["messages"].append({
            "role": "user",
            "content": message,
            "timestamp": asyncio.get_event_loop().time()
        })
        
        # Process message through constellation manager
        try:
            result = await self.constellation_manager.run_session(
                session_id=session_id,
                user_message=message,
                user_profile=self.user_profiles[user_id].to_dict(),
                framework=session["framework"],
                module_id=session["module_id"]
            )

            response = {
                "session_id": session_id,
                "response": result["response"].content if hasattr(result["response"], 'content') else str(result["response"]),
                "agent_path": result["agent_path"]
            }
        except Exception as e:
            # Fallback response if constellation processing fails
            response = {
                "session_id": session_id,
                "response": f"I'm your AI learning assistant for {session['framework']}. I encountered an issue processing your message: {str(e)}. Please try again.",
                "agent_path": ["system"]
            }
        
        # Store response in session history
        session["messages"].append({
            "role": "assistant",
            "content": response["response"],
            "timestamp": asyncio.get_event_loop().time()
        })
        
        # Update session metrics
        self._update_session_metrics(session_id, message, response)
        
        return response
        
    def _update_session_metrics(self, session_id: str, message: str, result: Dict):
        """
        Update session metrics based on interaction.
        
        Args:
            session_id: Session identifier
            message: User message
            result: Processing result
        """
        # In a real implementation, this would use more sophisticated analysis
        # For now, we'll use simple heuristics
        
        session = self.active_sessions[session_id]
        metrics = session["metrics"]
        
        # Update engagement score based on message length
        message_length = len(message)
        if message_length > 100:
            metrics.engagement_score = min(metrics.engagement_score + 0.1, 1.0)
        
        # Update completion rate if the message suggests task completion
        completion_indicators = ["completed", "finished", "done", "solved"]
        if any(indicator in message.lower() for indicator in completion_indicators):
            metrics.completion_rate = min(metrics.completion_rate + 0.2, 1.0)
            
    async def end_session(self, session_id: str) -> Dict:
        """
        End a learning session and save metrics.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session summary
        """
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
            
        session = self.active_sessions[session_id]
        
        # Calculate session duration
        duration = asyncio.get_event_loop().time() - session["start_time"]
        
        # Update temporal patterns with session metrics
        await self.temporal_state_manager.update_effectiveness(
            user_id=session["user_id"],
            framework=session["framework"],
            module_id=session["module_id"],
            constellation_type=session["constellation_type"],
            metrics=session["metrics"]
        )
        
        # Create session summary
        summary = {
            "session_id": session_id,
            "user_id": session["user_id"],
            "framework": session["framework"],
            "module_id": session["module_id"],
            "constellation_type": session["constellation_type"].value,
            "duration_seconds": duration,
            "message_count": len(session["messages"]) // 2,  # User messages only
            "effectiveness": session["metrics"].to_dict()
        }
        
        # Save session data
        sessions_path = self.data_path / "sessions"
        os.makedirs(sessions_path, exist_ok=True)
        
        try:
            with open(sessions_path / f"{session_id}.json", "w") as f:
                json.dump({
                    "summary": summary,
                    "messages": session["messages"]
                }, f, indent=2)
        except Exception as e:
            print(f"Error saving session {session_id}: {e}")
            
        # Remove from active sessions
        del self.active_sessions[session_id]
        
        return summary
        
    async def get_learning_progress(self, user_id: str, framework: str = None) -> Dict:
        """
        Get learning progress for a user.
        
        Args:
            user_id: User identifier
            framework: Optional filter by framework
            
        Returns:
            Learning progress summary
        """
        # Get user profile
        profile = await self.get_user_profile(user_id)
        if not profile:
            raise ValueError(f"User profile not found for {user_id}")
            
        # Get temporal patterns analysis
        patterns_analysis = await self.temporal_state_manager.analyze_patterns(user_id)
        
        # Load completed sessions
        sessions_path = self.data_path / "sessions"
        completed_sessions = []
        
        for file in sessions_path.glob("*.json"):
            try:
                with open(file, "r") as f:
                    session_data = json.load(f)
                    if session_data["summary"]["user_id"] == user_id:
                        if framework is None or session_data["summary"]["framework"] == framework:
                            completed_sessions.append(session_data["summary"])
            except Exception as e:
                print(f"Error loading session {file}: {e}")
                
        # Calculate overall progress metrics
        total_sessions = len(completed_sessions)
        avg_effectiveness = 0.0
        frameworks_studied = set()
        modules_completed = set()
        
        if total_sessions > 0:
            avg_effectiveness = sum(s["effectiveness"]["overall_score"] for s in completed_sessions) / total_sessions
            
        for session in completed_sessions:
            frameworks_studied.add(session["framework"])
            modules_completed.add(f"{session['framework']}:{session['module_id']}")
            
        # Create progress summary
        progress = {
            "user_id": user_id,
            "total_sessions": total_sessions,
            "frameworks_studied": list(frameworks_studied),
            "modules_completed": len(modules_completed),
            "average_effectiveness": avg_effectiveness,
            "constellation_performance": patterns_analysis.get("constellation_performance", {}),
            "recommendations": patterns_analysis.get("recommendations", [])
        }
        
        return progress 